// ==UserScript==
// @name         Linkvertise Bypass
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Automatically bypass Linkvertise links by extracting target URLs from redirect parameters
// <AUTHOR>
// @match        *://linkvertise.com/*
// @match        *://*.linkvertise.com/*
// @match        *://linkvertise.net/*
// @match        *://*.linkvertise.net/*
// @match        *://link-to.net/*
// @match        *://*.link-to.net/*
// @match        *://linkvertise.download/*
// @match        *://*.linkvertise.download/*
// @match        *://linkvertise.org/*
// @match        *://*.linkvertise.org/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        showNotifications: true,
        debugMode: false,
        redirectDelay: 500 // milliseconds
    };

    // Utility functions
    function log(message, type = 'info') {
        if (CONFIG.debugMode || type === 'error') {
            console.log(`[Linkvertise Bypass] ${message}`);
        }
    }

    function showNotification(message, type = 'info') {
        if (!CONFIG.showNotifications) return;
        
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#4CAF50'};
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            max-width: 300px;
            word-wrap: break-word;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // URL decoding and validation functions
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    function base64Decode(str) {
        try {
            // Handle URL-safe base64
            str = str.replace(/-/g, '+').replace(/_/g, '/');
            
            // Add padding if needed
            while (str.length % 4) {
                str += '=';
            }
            
            return atob(str);
        } catch (e) {
            log(`Base64 decode error: ${e.message}`, 'error');
            return null;
        }
    }

    function extractTargetUrl(url) {
        try {
            const urlObj = new URL(url);
            
            // Check for 'r' parameter (common in dynamic redirects)
            let encodedUrl = urlObj.searchParams.get('r');
            
            if (!encodedUrl) {
                // Check for other common parameter names
                const commonParams = ['url', 'target', 'redirect', 'link', 'u'];
                for (const param of commonParams) {
                    encodedUrl = urlObj.searchParams.get(param);
                    if (encodedUrl) break;
                }
            }
            
            if (!encodedUrl) {
                log('No redirect parameter found in URL');
                return null;
            }
            
            log(`Found encoded URL parameter: ${encodedUrl}`);
            
            // URL decode first (handle %3D, etc.)
            const urlDecoded = decodeURIComponent(encodedUrl);
            log(`URL decoded: ${urlDecoded}`);
            
            // Base64 decode
            const base64Decoded = base64Decode(urlDecoded);
            if (!base64Decoded) {
                log('Base64 decoding failed');
                return null;
            }
            
            log(`Base64 decoded: ${base64Decoded}`);
            
            // Validate the decoded URL
            if (!isValidUrl(base64Decoded)) {
                log('Decoded string is not a valid URL');
                return null;
            }
            
            return base64Decoded;
            
        } catch (e) {
            log(`Error extracting target URL: ${e.message}`, 'error');
            return null;
        }
    }

    // Main bypass function
    function bypassLinkvertise() {
        const currentUrl = window.location.href;
        log(`Processing URL: ${currentUrl}`);
        
        // Check if this looks like a Linkvertise redirect URL
        if (!currentUrl.includes('linkvertise') && !currentUrl.includes('link-to.net')) {
            log('Not a Linkvertise URL, skipping');
            return;
        }
        
        // Extract target URL
        const targetUrl = extractTargetUrl(currentUrl);
        
        if (!targetUrl) {
            log('Could not extract target URL from Linkvertise link');
            if (CONFIG.showNotifications) {
                showNotification('Could not bypass this Linkvertise link', 'error');
            }
            return;
        }
        
        log(`Target URL found: ${targetUrl}`);
        
        // Show notification
        if (CONFIG.showNotifications) {
            showNotification(`Bypassing Linkvertise... Redirecting to: ${new URL(targetUrl).hostname}`);
        }
        
        // Redirect after a short delay
        setTimeout(() => {
            log(`Redirecting to: ${targetUrl}`);
            window.location.href = targetUrl;
        }, CONFIG.redirectDelay);
    }

    // Alternative method: Check for common Linkvertise bypass patterns
    function checkForAlternativeBypass() {
        // Look for hidden elements or data attributes that might contain the real URL
        const selectors = [
            '[data-url]',
            '[data-target]',
            '[data-redirect]',
            '.hidden-url',
            '#target-url'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const url = element.getAttribute('data-url') || 
                           element.getAttribute('data-target') || 
                           element.getAttribute('data-redirect') ||
                           element.textContent;
                
                if (url && isValidUrl(url)) {
                    log(`Found alternative bypass URL: ${url}`);
                    return url;
                }
            }
        }
        
        return null;
    }

    // Initialize the bypass
    function init() {
        log('Linkvertise Bypass script initialized');
        
        // Try immediate bypass
        bypassLinkvertise();
        
        // If immediate bypass didn't work, try after DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    const alternativeUrl = checkForAlternativeBypass();
                    if (alternativeUrl) {
                        log(`Using alternative bypass method: ${alternativeUrl}`);
                        if (CONFIG.showNotifications) {
                            showNotification('Alternative bypass method found!');
                        }
                        setTimeout(() => {
                            window.location.href = alternativeUrl;
                        }, CONFIG.redirectDelay);
                    }
                }, 1000);
            });
        }
    }

    // Start the script
    init();

})();
