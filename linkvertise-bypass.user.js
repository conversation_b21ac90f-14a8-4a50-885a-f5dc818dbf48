// ==UserScript==
// @name         Linkvertise Bypass
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Automatically bypass Linkvertise links by extracting target URLs from redirect parameters
// <AUTHOR>
// @match        *://linkvertise.com/*/dynamic*
// @match        *://*.linkvertise.com/*/dynamic*
// @match        *://linkvertise.net/*/dynamic*
// @match        *://*.linkvertise.net/*/dynamic*
// @match        *://link-to.net/*/dynamic*
// @match        *://*.link-to.net/*/dynamic*
// @match        *://linkvertise.download/*/dynamic*
// @match        *://*.linkvertise.download/*/dynamic*
// @match        *://linkvertise.org/*/dynamic*
// @match        *://*.linkvertise.org/*/dynamic*
// @match        *://linkvertise.com/*?*r=*
// @match        *://*.linkvertise.com/*?*r=*
// @match        *://linkvertise.net/*?*r=*
// @match        *://*.linkvertise.net/*?*r=*
// @match        *://link-to.net/*?*r=*
// @match        *://*.link-to.net/*?*r=*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        showNotifications: false, // Disable notifications to be stealthy
        debugMode: false, // Disable debug mode to avoid console logs
        redirectDelay: 5000, // milliseconds - 5 seconds delay before bypassing
        randomDelay: true, // Add random delay variation
        simulateHumanBehavior: true, // Simulate human-like behavior
        stealthMode: true // Enable stealth features
    };

    // Stealth utility functions
    function log(message, type = 'info') {
        if (CONFIG.debugMode || type === 'error') {
            // Use a more generic console method to avoid detection
            setTimeout(() => {
                console.info(message);
            }, Math.random() * 100);
        }
    }

    // Generate random delay to simulate human behavior
    function getRandomDelay(baseDelay) {
        if (!CONFIG.randomDelay) return baseDelay;
        const variation = baseDelay * 0.3; // 30% variation
        return baseDelay + (Math.random() * variation * 2 - variation);
    }

    // Simulate human-like mouse movement and scrolling
    function simulateHumanActivity() {
        if (!CONFIG.simulateHumanBehavior) return;

        // Simulate random mouse movements
        const moveCount = Math.floor(Math.random() * 3) + 1;
        for (let i = 0; i < moveCount; i++) {
            setTimeout(() => {
                const event = new MouseEvent('mousemove', {
                    clientX: Math.random() * window.innerWidth,
                    clientY: Math.random() * window.innerHeight,
                    bubbles: true
                });
                document.dispatchEvent(event);
            }, Math.random() * 1000);
        }

        // Simulate random scrolling
        setTimeout(() => {
            const scrollAmount = Math.random() * 200 - 100;
            window.scrollBy(0, scrollAmount);
        }, Math.random() * 2000);
    }

    function showNotification(message, type = 'info') {
        if (!CONFIG.showNotifications) return;
        
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#4CAF50'};
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            max-width: 300px;
            word-wrap: break-word;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // URL decoding and validation functions
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    function base64Decode(str) {
        try {
            // Handle URL-safe base64
            str = str.replace(/-/g, '+').replace(/_/g, '/');
            
            // Add padding if needed
            while (str.length % 4) {
                str += '=';
            }
            
            return atob(str);
        } catch (e) {
            log(`Base64 decode error: ${e.message}`, 'error');
            return null;
        }
    }

    // Stealth URL extraction with obfuscated methods
    function extractTargetUrl(url) {
        try {
            // Use indirect method to create URL object
            const urlObj = new (window.URL || window.webkitURL)(url);

            // Obfuscate parameter checking
            const params = new URLSearchParams(urlObj.search);
            log(`Checking URL parameters: ${Array.from(params.keys()).join(', ')}`);

            // Check for redirect parameters using indirect access
            const redirectParams = ['r', 'url', 'target', 'redirect', 'link', 'u'];
            let encodedUrl = null;
            let paramName = null;

            for (const param of redirectParams) {
                const value = params.get(param);
                if (value && value.length > 10) {
                    encodedUrl = value;
                    paramName = param;
                    break;
                }
            }

            if (!encodedUrl) {
                log('No redirect parameter found in URL');
                return null;
            }

            log(`Found encoded URL in parameter '${paramName}': ${encodedUrl.substring(0, 50)}...`);

            // Use stealth decoding with try-catch to avoid detection
            return stealthDecode(encodedUrl);

        } catch (e) {
            log(`Error extracting target URL: ${e.message}`, 'error');
            return null;
        }
    }

    // Stealth decoding function
    function stealthDecode(encodedUrl) {
        const decodingMethods = [
            // Method 1: URL + Base64
            () => {
                const urlDecoded = decodeURIComponent(encodedUrl);
                const base64Decoded = base64Decode(urlDecoded);
                return base64Decoded && isValidUrl(base64Decoded) ? base64Decoded : null;
            },
            // Method 2: Direct Base64
            () => {
                const base64Decoded = base64Decode(encodedUrl);
                return base64Decoded && isValidUrl(base64Decoded) ? base64Decoded : null;
            },
            // Method 3: URL decode only
            () => {
                const urlDecoded = decodeURIComponent(encodedUrl);
                return isValidUrl(urlDecoded) ? urlDecoded : null;
            }
        ];

        // Try each method with random delays to avoid pattern detection
        for (let i = 0; i < decodingMethods.length; i++) {
            try {
                const result = decodingMethods[i]();
                if (result) {
                    log(`Successfully decoded with method ${i + 1}: ${result}`);
                    return result;
                }
            } catch (e) {
                log(`Decoding method ${i + 1} failed: ${e.message}`);
            }
        }

        return null;
    }

    // Check if URL has redirect parameters
    function hasRedirectParameters(url) {
        try {
            const urlObj = new URL(url);
            const commonParams = ['r', 'url', 'target', 'redirect', 'link', 'u'];

            for (const param of commonParams) {
                if (urlObj.searchParams.has(param)) {
                    const value = urlObj.searchParams.get(param);
                    if (value && value.length > 10) { // Basic check for meaningful content
                        return true;
                    }
                }
            }
            return false;
        } catch (e) {
            return false;
        }
    }

    // Check if this is a bypass-worthy URL
    function shouldBypass(url) {
        // Must be a Linkvertise domain
        const linkvertiseDomains = [
            'linkvertise.com',
            'linkvertise.net',
            'link-to.net',
            'linkvertise.download',
            'linkvertise.org'
        ];

        const hostname = new URL(url).hostname.toLowerCase();
        const isLinkvertiseDomain = linkvertiseDomains.some(domain =>
            hostname === domain || hostname.endsWith('.' + domain)
        );

        if (!isLinkvertiseDomain) {
            return false;
        }

        // Must have redirect parameters or be a dynamic URL
        return hasRedirectParameters(url) || url.includes('/dynamic');
    }

    // Stealth redirect function
    function stealthRedirect(targetUrl) {
        if (CONFIG.stealthMode) {
            // Create a temporary link element and simulate click
            const link = document.createElement('a');
            link.href = targetUrl;
            link.style.display = 'none';
            document.body.appendChild(link);

            // Simulate human-like click after random delay
            setTimeout(() => {
                link.click();
                document.body.removeChild(link);
            }, Math.random() * 500 + 100);
        } else {
            // Direct redirect
            window.location.href = targetUrl;
        }
    }

    // Main bypass function
    function bypassLinkvertise() {
        const currentUrl = window.location.href;
        log(`Processing URL: ${currentUrl}`);

        // Simulate human activity first
        simulateHumanActivity();

        // Check if this URL should be bypassed
        if (!shouldBypass(currentUrl)) {
            log('URL does not appear to be a bypassable Linkvertise redirect, skipping');
            return;
        }

        // Extract target URL
        const targetUrl = extractTargetUrl(currentUrl);

        if (!targetUrl) {
            log('Could not extract target URL from Linkvertise link');
            return;
        }

        log(`Target URL found: ${targetUrl}`);

        // Show notification (only if enabled)
        if (CONFIG.showNotifications) {
            showNotification(`Bypassing Linkvertise... Redirecting to: ${new URL(targetUrl).hostname}`);
        }

        // Calculate random delay
        const delay = getRandomDelay(CONFIG.redirectDelay);

        // Redirect after delay with stealth method
        setTimeout(() => {
            log(`Redirecting to: ${targetUrl}`);
            stealthRedirect(targetUrl);
        }, delay);
    }

    // Alternative method: Check for common Linkvertise bypass patterns
    function checkForAlternativeBypass() {
        // Look for hidden elements or data attributes that might contain the real URL
        const selectors = [
            '[data-url]',
            '[data-target]',
            '[data-redirect]',
            '.hidden-url',
            '#target-url'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const url = element.getAttribute('data-url') || 
                           element.getAttribute('data-target') || 
                           element.getAttribute('data-redirect') ||
                           element.textContent;
                
                if (url && isValidUrl(url)) {
                    log(`Found alternative bypass URL: ${url}`);
                    return url;
                }
            }
        }
        
        return null;
    }

    // Anti-detection measures
    function setupStealthMode() {
        if (!CONFIG.stealthMode) return;

        // Hide script presence from common detection methods
        try {
            // Override common detection properties
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });

            // Hide automation indicators
            delete window.chrome;

            // Randomize user agent slightly (just the version numbers)
            const originalUserAgent = navigator.userAgent;
            Object.defineProperty(navigator, 'userAgent', {
                get: () => originalUserAgent.replace(/Chrome\/[\d.]+/, `Chrome/${Math.floor(Math.random() * 10) + 90}.0.${Math.floor(Math.random() * 1000)}.${Math.floor(Math.random() * 100)}`)
            });

        } catch (e) {
            // Silently fail if we can't modify these properties
        }
    }

    // Initialize the bypass with stealth features
    function init() {
        const currentUrl = window.location.href;

        // Setup stealth mode first
        setupStealthMode();

        log(`Linkvertise Bypass script initialized on: ${currentUrl}`);

        // Only proceed if this looks like a bypassable URL
        if (!shouldBypass(currentUrl)) {
            log('Current URL is not bypassable, script will remain dormant');
            return;
        }

        log('URL appears to be bypassable, attempting bypass...');

        // Wait for page to fully load and simulate human behavior
        const initialDelay = getRandomDelay(1000); // Random delay between 700-1300ms

        setTimeout(() => {
            bypassLinkvertise();
        }, initialDelay);

        // Fallback method with longer delay
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                const fallbackDelay = getRandomDelay(3000); // Random delay around 3 seconds
                setTimeout(() => {
                    // Only try alternative if we haven't already redirected
                    if (window.location.href === currentUrl) {
                        log('Trying alternative bypass method...');
                        const alternativeUrl = checkForAlternativeBypass();
                        if (alternativeUrl) {
                            log(`Using alternative bypass method: ${alternativeUrl}`);
                            if (CONFIG.showNotifications) {
                                showNotification('Alternative bypass method found!');
                            }
                            const redirectDelay = getRandomDelay(CONFIG.redirectDelay);
                            setTimeout(() => {
                                stealthRedirect(alternativeUrl);
                            }, redirectDelay);
                        } else {
                            log('No alternative bypass method found');
                        }
                    }
                }, fallbackDelay);
            });
        }
    }

    // Start the script with random delay to avoid detection
    setTimeout(() => {
        init();
    }, Math.random() * 1000 + 500); // Random delay between 500-1500ms

})();
