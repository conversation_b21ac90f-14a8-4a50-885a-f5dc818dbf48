// ==UserScript==
// @name         Linkvertise Bypass
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  Automatically bypass Linkvertise links by extracting target URLs from redirect parameters
// <AUTHOR>
// @match        *://linkvertise.com/*
// @match        *://*.linkvertise.com/*
// @match        *://linkvertise.net/*
// @match        *://*.linkvertise.net/*
// @match        *://link-to.net/*
// @match        *://*.link-to.net/*
// @match        *://linkvertise.download/*
// @match        *://*.linkvertise.download/*
// @match        *://linkvertise.org/*
// @match        *://*.linkvertise.org/*
// @grant        none
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        showNotifications: true,
        debugMode: true, // Enable debug mode to see what's happening
        redirectDelay: 1000 // milliseconds - increased delay for debugging
    };

    // Utility functions
    function log(message, type = 'info') {
        if (CONFIG.debugMode || type === 'error') {
            console.log(`[Linkvertise Bypass] ${message}`);
        }
    }

    function showNotification(message, type = 'info') {
        if (!CONFIG.showNotifications) return;
        
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#ff4444' : '#4CAF50'};
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            max-width: 300px;
            word-wrap: break-word;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // URL decoding and validation functions
    function isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    function base64Decode(str) {
        try {
            // Handle URL-safe base64
            str = str.replace(/-/g, '+').replace(/_/g, '/');
            
            // Add padding if needed
            while (str.length % 4) {
                str += '=';
            }
            
            return atob(str);
        } catch (e) {
            log(`Base64 decode error: ${e.message}`, 'error');
            return null;
        }
    }

    function extractTargetUrl(url) {
        try {
            const urlObj = new URL(url);
            log(`Checking URL parameters: ${Array.from(urlObj.searchParams.keys()).join(', ')}`);

            // Check for 'r' parameter (common in dynamic redirects)
            let encodedUrl = urlObj.searchParams.get('r');
            let paramName = 'r';

            if (!encodedUrl) {
                // Check for other common parameter names
                const commonParams = ['url', 'target', 'redirect', 'link', 'u'];
                for (const param of commonParams) {
                    encodedUrl = urlObj.searchParams.get(param);
                    if (encodedUrl) {
                        paramName = param;
                        break;
                    }
                }
            }

            if (!encodedUrl) {
                log('No redirect parameter found in URL');
                return null;
            }

            log(`Found encoded URL in parameter '${paramName}': ${encodedUrl.substring(0, 50)}...`);

            // Try multiple decoding approaches
            let decodedUrl = null;

            // Approach 1: URL decode then Base64 decode
            try {
                const urlDecoded = decodeURIComponent(encodedUrl);
                log(`URL decoded: ${urlDecoded.substring(0, 50)}...`);

                const base64Decoded = base64Decode(urlDecoded);
                if (base64Decoded && isValidUrl(base64Decoded)) {
                    decodedUrl = base64Decoded;
                    log(`Successfully decoded with URL+Base64: ${decodedUrl}`);
                }
            } catch (e) {
                log(`URL+Base64 decode failed: ${e.message}`);
            }

            // Approach 2: Direct Base64 decode (if URL decode failed)
            if (!decodedUrl) {
                try {
                    const base64Decoded = base64Decode(encodedUrl);
                    if (base64Decoded && isValidUrl(base64Decoded)) {
                        decodedUrl = base64Decoded;
                        log(`Successfully decoded with direct Base64: ${decodedUrl}`);
                    }
                } catch (e) {
                    log(`Direct Base64 decode failed: ${e.message}`);
                }
            }

            // Approach 3: Check if it's already a valid URL (just URL encoded)
            if (!decodedUrl) {
                try {
                    const urlDecoded = decodeURIComponent(encodedUrl);
                    if (isValidUrl(urlDecoded)) {
                        decodedUrl = urlDecoded;
                        log(`Successfully decoded with URL decode only: ${decodedUrl}`);
                    }
                } catch (e) {
                    log(`URL decode only failed: ${e.message}`);
                }
            }

            return decodedUrl;

        } catch (e) {
            log(`Error extracting target URL: ${e.message}`, 'error');
            return null;
        }
    }

    // Check if URL has redirect parameters
    function hasRedirectParameters(url) {
        try {
            const urlObj = new URL(url);
            const commonParams = ['r', 'url', 'target', 'redirect', 'link', 'u'];

            for (const param of commonParams) {
                if (urlObj.searchParams.has(param)) {
                    const value = urlObj.searchParams.get(param);
                    if (value && value.length > 10) { // Basic check for meaningful content
                        return true;
                    }
                }
            }
            return false;
        } catch (e) {
            return false;
        }
    }

    // Check if this is a bypass-worthy URL
    function shouldBypass(url) {
        // Must be a Linkvertise domain
        const linkvertiseDomains = [
            'linkvertise.com',
            'linkvertise.net',
            'link-to.net',
            'linkvertise.download',
            'linkvertise.org'
        ];

        const hostname = new URL(url).hostname.toLowerCase();
        const isLinkvertiseDomain = linkvertiseDomains.some(domain =>
            hostname === domain || hostname.endsWith('.' + domain)
        );

        if (!isLinkvertiseDomain) {
            return false;
        }

        // Must have redirect parameters or be a dynamic URL
        return hasRedirectParameters(url) || url.includes('/dynamic');
    }

    // Main bypass function
    function bypassLinkvertise() {
        const currentUrl = window.location.href;
        log(`Processing URL: ${currentUrl}`);

        // Check if this URL should be bypassed
        if (!shouldBypass(currentUrl)) {
            log('URL does not appear to be a bypassable Linkvertise redirect, skipping');
            return;
        }

        // Extract target URL
        const targetUrl = extractTargetUrl(currentUrl);

        if (!targetUrl) {
            log('Could not extract target URL from Linkvertise link');
            // Don't show error notification for main pages
            return;
        }

        log(`Target URL found: ${targetUrl}`);

        // Show notification
        if (CONFIG.showNotifications) {
            showNotification(`Bypassing Linkvertise... Redirecting to: ${new URL(targetUrl).hostname}`);
        }

        // Redirect after a short delay
        setTimeout(() => {
            log(`Redirecting to: ${targetUrl}`);
            window.location.href = targetUrl;
        }, CONFIG.redirectDelay);
    }

    // Alternative method: Check for common Linkvertise bypass patterns
    function checkForAlternativeBypass() {
        // Look for hidden elements or data attributes that might contain the real URL
        const selectors = [
            '[data-url]',
            '[data-target]',
            '[data-redirect]',
            '.hidden-url',
            '#target-url'
        ];
        
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                const url = element.getAttribute('data-url') || 
                           element.getAttribute('data-target') || 
                           element.getAttribute('data-redirect') ||
                           element.textContent;
                
                if (url && isValidUrl(url)) {
                    log(`Found alternative bypass URL: ${url}`);
                    return url;
                }
            }
        }
        
        return null;
    }

    // Initialize the bypass
    function init() {
        const currentUrl = window.location.href;
        log(`Linkvertise Bypass script initialized on: ${currentUrl}`);

        // Only proceed if this looks like a bypassable URL
        if (!shouldBypass(currentUrl)) {
            log('Current URL is not bypassable, script will remain dormant');
            return;
        }

        log('URL appears to be bypassable, attempting bypass...');

        // Try immediate bypass
        setTimeout(() => {
            bypassLinkvertise();
        }, 500); // Small delay to ensure page is ready

        // If immediate bypass didn't work, try after DOM is loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => {
                    // Only try alternative if we haven't already redirected
                    if (window.location.href === currentUrl) {
                        log('Trying alternative bypass method...');
                        const alternativeUrl = checkForAlternativeBypass();
                        if (alternativeUrl) {
                            log(`Using alternative bypass method: ${alternativeUrl}`);
                            if (CONFIG.showNotifications) {
                                showNotification('Alternative bypass method found!');
                            }
                            setTimeout(() => {
                                window.location.href = alternativeUrl;
                            }, CONFIG.redirectDelay);
                        } else {
                            log('No alternative bypass method found');
                        }
                    }
                }, 2000);
            });
        }
    }

    // Start the script
    init();

})();
