// Debug script to test Linkvertise URL detection and decoding
// Run this in browser console on a Linkvertise page to see what's happening

function debugLinkvertiseUrl(url = window.location.href) {
    console.log('=== Linkvertise Debug Analysis ===');
    console.log('URL:', url);
    
    try {
        const urlObj = new URL(url);
        console.log('Hostname:', urlObj.hostname);
        console.log('Pathname:', urlObj.pathname);
        console.log('Search params:', urlObj.search);
        
        // Check all parameters
        console.log('\nAll URL parameters:');
        for (const [key, value] of urlObj.searchParams) {
            console.log(`  ${key}: ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}`);
        }
        
        // Check for redirect parameters
        const redirectParams = ['r', 'url', 'target', 'redirect', 'link', 'u'];
        let foundParam = null;
        let encodedUrl = null;
        
        for (const param of redirectParams) {
            const value = urlObj.searchParams.get(param);
            if (value) {
                foundParam = param;
                encodedUrl = value;
                break;
            }
        }
        
        if (!foundParam) {
            console.log('\n❌ No redirect parameters found');
            return null;
        }
        
        console.log(`\n✅ Found redirect parameter: ${foundParam}`);
        console.log('Encoded value:', encodedUrl.substring(0, 100) + (encodedUrl.length > 100 ? '...' : ''));
        
        // Try decoding
        console.log('\n=== Decoding Attempts ===');
        
        // Method 1: URL decode + Base64 decode
        try {
            const urlDecoded = decodeURIComponent(encodedUrl);
            console.log('1. URL decoded:', urlDecoded.substring(0, 100) + (urlDecoded.length > 100 ? '...' : ''));
            
            const base64Decoded = atob(urlDecoded.replace(/-/g, '+').replace(/_/g, '/'));
            console.log('1. Base64 decoded:', base64Decoded);
            
            if (isValidUrl(base64Decoded)) {
                console.log('✅ Method 1 SUCCESS:', base64Decoded);
                return base64Decoded;
            } else {
                console.log('❌ Method 1 failed: Not a valid URL');
            }
        } catch (e) {
            console.log('❌ Method 1 failed:', e.message);
        }
        
        // Method 2: Direct Base64 decode
        try {
            const base64Decoded = atob(encodedUrl.replace(/-/g, '+').replace(/_/g, '/'));
            console.log('2. Direct Base64 decoded:', base64Decoded);
            
            if (isValidUrl(base64Decoded)) {
                console.log('✅ Method 2 SUCCESS:', base64Decoded);
                return base64Decoded;
            } else {
                console.log('❌ Method 2 failed: Not a valid URL');
            }
        } catch (e) {
            console.log('❌ Method 2 failed:', e.message);
        }
        
        // Method 3: Just URL decode
        try {
            const urlDecoded = decodeURIComponent(encodedUrl);
            console.log('3. URL decode only:', urlDecoded);
            
            if (isValidUrl(urlDecoded)) {
                console.log('✅ Method 3 SUCCESS:', urlDecoded);
                return urlDecoded;
            } else {
                console.log('❌ Method 3 failed: Not a valid URL');
            }
        } catch (e) {
            console.log('❌ Method 3 failed:', e.message);
        }
        
        console.log('\n❌ All decoding methods failed');
        return null;
        
    } catch (e) {
        console.log('❌ Error analyzing URL:', e.message);
        return null;
    }
}

function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// Auto-run on current page
console.log('Running Linkvertise debug analysis...');
const result = debugLinkvertiseUrl();

if (result) {
    console.log('\n🎉 SUCCESS! Target URL found:', result);
    console.log('You can copy this URL to bypass the redirect');
} else {
    console.log('\n💡 This might not be a bypassable Linkvertise URL, or it uses a different encoding method');
    console.log('Try running: debugLinkvertiseUrl("your-linkvertise-url-here")');
}
