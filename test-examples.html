<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Linkvertise Bypass Test Examples</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .example {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007cba;
        }
        .url {
            background: #e8e8e8;
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            word-break: break-all;
            margin: 5px 0;
        }
        .decoded {
            background: #d4edda;
            color: #155724;
            padding: 8px;
            border-radius: 3px;
            margin: 5px 0;
        }
        .button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .button:hover {
            background: #005a87;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
    </style>
</head>
<body>
    <h1>Linkvertise Bypass Script - Test Examples</h1>
    
    <div class="warning">
        <strong>Note:</strong> This page demonstrates how the Tampermonkey script works. 
        The actual script would automatically redirect when visiting real Linkvertise URLs.
    </div>

    <h2>How the Script Works</h2>
    <p>The Tampermonkey script automatically detects Linkvertise URLs and extracts the target URL using these steps:</p>
    <ol>
        <li>Extract the redirect parameter (usually 'r') from the URL</li>
        <li>URL-decode the parameter value</li>
        <li>Base64-decode the result</li>
        <li>Redirect to the decoded target URL</li>
    </ol>

    <h2>Example Linkvertise URL Formats</h2>

    <div class="example">
        <h3>Example 1: Basic Dynamic Redirect</h3>
        <p><strong>Linkvertise URL:</strong></p>
        <div class="url">https://linkvertise.com/dynamic?r=aHR0cHM6Ly9leGFtcGxlLmNvbQ%3D%3D</div>
        
        <p><strong>Breakdown:</strong></p>
        <ul>
            <li>Parameter 'r': <code>aHR0cHM6Ly9leGFtcGxlLmNvbQ%3D%3D</code></li>
            <li>URL-decoded: <code>aHR0cHM6Ly9leGFtcGxlLmNvbQ==</code></li>
            <li>Base64-decoded: <code>https://example.com</code></li>
        </ul>
        
        <div class="decoded">Target URL: https://example.com</div>
    </div>

    <div class="example">
        <h3>Example 2: Complex URL with Path</h3>
        <p><strong>Linkvertise URL:</strong></p>
        <div class="url">https://linkvertise.net/12345/1/dynamic?r=aHR0cHM6Ly9naXRodWIuY29tL3VzZXIvcmVwbw%3D%3D</div>
        
        <p><strong>Breakdown:</strong></p>
        <ul>
            <li>Parameter 'r': <code>aHR0cHM6Ly9naXRodWIuY29tL3VzZXIvcmVwbw%3D%3D</code></li>
            <li>URL-decoded: <code>aHR0cHM6Ly9naXRodWIuY29tL3VzZXIvcmVwbw==</code></li>
            <li>Base64-decoded: <code>https://github.com/user/repo</code></li>
        </ul>
        
        <div class="decoded">Target URL: https://github.com/user/repo</div>
    </div>

    <h2>Manual Testing</h2>
    <p>You can test the decoding logic manually using these JavaScript functions:</p>
    
    <textarea id="testUrl" placeholder="Paste a Linkvertise URL here..." style="width: 100%; height: 60px; margin: 10px 0;"></textarea>
    <button class="button" onclick="testDecode()">Test Decode</button>
    <button class="button" onclick="clearResult()">Clear</button>
    
    <div id="result" style="margin-top: 20px;"></div>

    <h2>Installation Instructions</h2>
    <ol>
        <li>Install Tampermonkey browser extension</li>
        <li>Copy the script from <code>linkvertise-bypass.user.js</code></li>
        <li>Create a new script in Tampermonkey</li>
        <li>Paste the script content and save</li>
        <li>The script will automatically activate on Linkvertise domains</li>
    </ol>

    <script>
        function base64Decode(str) {
            try {
                // Handle URL-safe base64
                str = str.replace(/-/g, '+').replace(/_/g, '/');
                
                // Add padding if needed
                while (str.length % 4) {
                    str += '=';
                }
                
                return atob(str);
            } catch (e) {
                return null;
            }
        }

        function extractTargetUrl(url) {
            try {
                const urlObj = new URL(url);
                
                // Check for 'r' parameter
                let encodedUrl = urlObj.searchParams.get('r');
                
                if (!encodedUrl) {
                    return null;
                }
                
                // URL decode first
                const urlDecoded = decodeURIComponent(encodedUrl);
                
                // Base64 decode
                const base64Decoded = base64Decode(urlDecoded);
                
                return base64Decoded;
                
            } catch (e) {
                return null;
            }
        }

        function testDecode() {
            const url = document.getElementById('testUrl').value.trim();
            const resultDiv = document.getElementById('result');
            
            if (!url) {
                resultDiv.innerHTML = '<div style="color: red;">Please enter a URL to test.</div>';
                return;
            }
            
            const targetUrl = extractTargetUrl(url);
            
            if (targetUrl) {
                resultDiv.innerHTML = `
                    <div style="background: #d4edda; color: #155724; padding: 10px; border-radius: 5px;">
                        <strong>Success!</strong><br>
                        Target URL: <a href="${targetUrl}" target="_blank">${targetUrl}</a>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 5px;">
                        <strong>Failed to decode URL.</strong><br>
                        This might not be a valid Linkvertise URL or it uses a different encoding method.
                    </div>
                `;
            }
        }

        function clearResult() {
            document.getElementById('testUrl').value = '';
            document.getElementById('result').innerHTML = '';
        }
    </script>
</body>
</html>
