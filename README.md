# Linkvertise Bypass Tampermonkey Script

A Tampermonkey userscript that automatically bypasses Linkvertise links by detecting and extracting target URLs from redirect parameters.

## Features

- **Automatic Detection**: Works on all Linkvertise domains (linkvertise.com, linkvertise.net, link-to.net, etc.)
- **Smart Decoding**: Handles URL-encoded and Base64-encoded redirect parameters
- **Multiple Bypass Methods**: Primary method extracts from URL parameters, fallback method searches DOM elements
- **User-Friendly**: Shows notifications and handles errors gracefully
- **No Dependencies**: Pure vanilla JavaScript, no external libraries required

## Installation

1. **Install Tampermonkey**:
   - Chrome: [Tampermonkey Chrome Extension](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
   - Firefox: [Tampermonkey Firefox Add-on](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
   - Edge: [Tampermonkey Edge Extension](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd)

2. **Install the Script**:
   - Open Tampermonkey dashboard
   - Click "Create a new script"
   - Replace the default content with the contents of `linkvertise-bypass.user.js`
   - Save the script (Ctrl+S)

## How It Works

### Primary Bypass Method
1. Detects Linkvertise URLs with redirect parameters
2. Extracts the `r` parameter (or other common parameter names)
3. URL-decodes the parameter value (handles %3D, etc.)
4. Base64-decodes the result to reveal the target URL
5. Automatically redirects to the decoded target URL

### Fallback Method
If the primary method fails, the script:
1. Searches for DOM elements that might contain the real URL
2. Checks data attributes and hidden elements
3. Validates any found URLs before redirecting

### Supported URL Formats
- `https://linkvertise.com/dynamic?r=<base64_encoded_url>`
- `https://linkvertise.net/12345/1/dynamic?r=<encoded_url>`
- URLs with other parameter names: `url`, `target`, `redirect`, `link`, `u`

## Configuration

You can modify the script behavior by editing the `CONFIG` object:

```javascript
const CONFIG = {
    showNotifications: true,  // Show bypass notifications
    debugMode: false,        // Enable debug logging
    redirectDelay: 500       // Delay before redirect (milliseconds)
};
```

## Supported Domains

The script activates on:
- linkvertise.com and all subdomains
- linkvertise.net and all subdomains
- link-to.net and all subdomains
- linkvertise.download and all subdomains
- linkvertise.org and all subdomains

## Error Handling

The script includes comprehensive error handling:
- Invalid Base64 encoding
- Malformed URLs
- Missing redirect parameters
- Network errors

If bypass fails, you'll see an error notification and the original page will remain loaded.

## Privacy & Security

- **No Data Collection**: The script doesn't send any data to external servers
- **Local Processing**: All URL decoding happens locally in your browser
- **No Permissions**: Uses `@grant none` - no special Tampermonkey permissions required

## Troubleshooting

### Script Not Working?
1. Check if Tampermonkey is enabled
2. Verify the script is enabled in Tampermonkey dashboard
3. Enable debug mode in the CONFIG to see console logs
4. Check browser console for error messages

### Still Getting Linkvertise Pages?
1. Some Linkvertise links may use different encoding methods
2. Try refreshing the page
3. Check if the URL actually contains redirect parameters

### False Positives?
If the script activates on legitimate Linkvertise pages:
1. The script only redirects if it finds valid redirect parameters
2. You can disable notifications in the CONFIG if they're annoying

## Contributing

To improve the script:
1. Test with different Linkvertise URL formats
2. Add support for new parameter names or encoding methods
3. Improve error handling and user feedback

## Legal Notice

This script is for educational purposes. Users are responsible for complying with the terms of service of websites they visit. The script simply automates the process of extracting publicly available redirect information from URLs.
